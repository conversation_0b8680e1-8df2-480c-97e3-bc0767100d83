# Test script to verify configuration loading
function Get-Config {
    try {
        $configPath = ".\config.json"
        if (Test-Path $configPath) {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            return $configContent
        } else {
            Write-Warning "Config file not found at $configPath"
            return $null
        }
    } catch {
        Write-Warning "Error loading config file: $_"
        return $null
    }
}

# Load and test configuration
$config = Get-Config

if ($config) {
    Write-Host "Configuration loaded successfully!" -ForegroundColor Green
    Write-Host "Discord Raider Role ID: $($config.discord.raider_role_id)" -ForegroundColor Cyan
    Write-Host "Discord Guild ID: $($config.discord.guild_id)" -ForegroundColor Cyan
    Write-Host "Warcraft Logs Guild ID: $($config.warcraft_logs.guild_id)" -ForegroundColor Cyan
    Write-Host "Tier Item Level: $($config.game_settings.tier_ilvl)" -ForegroundColor Cyan
    Write-Host "App Host: $($config.app_settings.host)" -ForegroundColor Cyan
    Write-Host "App Port: $($config.app_settings.port)" -ForegroundColor Cyan
} else {
    Write-Host "Failed to load configuration!" -ForegroundColor Red
}
