{% extends "base.html" %}

{% block title %}Tier Sets - Uproar{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-gem text-accent me-2"></i>
                    Tier Sets Overview
                </h1>
            </div>
            <div>
                <select id="dateSelect" class="form-select form-select-sm" style="min-width: 150px;">
                    <option value="current" {% if selected_date == 'current' %}selected{% endif %}>Current</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Compact Tier Overview -->
<div class="row mb-3">
    <div class="col-12">
        <div class="tier-summary-compact p-3">
            <!-- Tier Token Distribution -->
            <div class="d-flex justify-content-between align-items-center mb-2">
                
               
            </div>

            <!-- All counters in one row -->
            <div class="tier-counters-row d-flex gap-2">
                <!-- Tier Token Counters -->
                <div class="tier-counter-compact tier-mystic">
                    <div class="counter-value">0</div>
                    <div class="counter-label">Mystic</div>
                </div>
                <div class="tier-counter-compact tier-venerated">
                    <div class="counter-value">0</div>
                    <div class="counter-label">Venerated</div>
                </div>
                <div class="tier-counter-compact tier-zenith">
                    <div class="counter-value">0</div>
                    <div class="counter-label">Zenith</div>
                </div>
                <div class="tier-counter-compact tier-dreadful">
                    <div class="counter-value">0</div>
                    <div class="counter-label">Dreadful</div>
                </div>

                <!-- Separator -->
                <div class="counter-separator"></div>

                <!-- Set Bonus Counters -->
                <div class="set-counter-compact no-set">
                    <div class="counter-value"><span id="no-set-count">0</span>/<span id="total-raiders">0</span></div>
                    <div class="counter-label">No Set</div>
                </div>
                <div class="set-counter-compact one-set">
                    <div class="counter-value"><span id="one-set-count">0</span>/<span id="total-raiders-1">0</span></div>
                    <div class="counter-label">1-Set</div>
                </div>
                <div class="set-counter-compact two-set">
                    <div class="counter-value"><span id="two-set-count">0</span>/<span id="total-raiders-2">0</span></div>
                    <div class="counter-label">2-Set</div>
                </div>
                <div class="set-counter-compact three-set">
                    <div class="counter-value"><span id="three-set-count">0</span>/<span id="total-raiders-3">0</span></div>
                    <div class="counter-label">3-Set</div>
                </div>
                <div class="set-counter-compact four-set">
                    <div class="counter-value"><span id="four-set-count">0</span>/<span id="total-raiders-4">0</span></div>
                    <div class="counter-label">4-Set</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tier Sets Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Character Tier Sets
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search characters..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="character-name" class="text-start">
                                    <i class="fas fa-user me-1"></i>Character
                                </th>
                                <th data-sort="token" class="text-start">
                                    <i class="fas fa-coins me-1"></i>Token
                                </th>
                                <th data-sort="head" class="text-center">
                                    <i class="fas fa-hat-wizard me-1"></i>Head
                                </th>
                                <th data-sort="shoulders" class="text-center">
                                    <i class="fas fa-tshirt me-1"></i>Shoulders
                                </th>
                                <th data-sort="chest" class="text-center">
                                    <i class="fas fa-vest me-1"></i>Chest
                                </th>
                                <th data-sort="hands" class="text-center">
                                    <i class="fas fa-hand-paper me-1"></i>Hands
                                </th>
                                <th data-sort="legs" class="text-center">
                                    <i class="fas fa-socks me-1"></i>Legs
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for character in characters %}
                            <tr>
                                <td class="text-start">
                                    <span class="fw-bold">{{ character.name }}</span>
                                </td>
                                <td class="text-start">
                                    <span class="badge bg-tertiary border-custom tier-token tier-token-{{ character.token.lower() }}">
                                        {{ character.token }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="tier-piece {% if character.head == 1 %}has-piece{% else %}no-piece{% endif %}">
                                        {% if character.head == 1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="tier-piece {% if character.shoulders == 1 %}has-piece{% else %}no-piece{% endif %}">
                                        {% if character.shoulders == 1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="tier-piece {% if character.chest == 1 %}has-piece{% else %}no-piece{% endif %}">
                                        {% if character.chest == 1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="tier-piece {% if character.hands == 1 %}has-piece{% else %}no-piece{% endif %}">
                                        {% if character.hands == 1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="tier-piece {% if character.legs == 1 %}has-piece{% else %}no-piece{% endif %}">
                                        {% if character.legs == 1 %}
                                            <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table-modern');
    const headers = table.querySelectorAll('th[data-sort]');
    let currentSort = { column: null, asc: true };

    // Load historical dates
    fetch('/get_historical_dates')
        .then(response => response.json())
        .then(data => {
            const dateSelect = document.getElementById('dateSelect');
            data.dates.forEach(date => {
                const option = document.createElement('option');
                option.value = date;
                // Format date for display (YYYY-MM-DD)
                const displayDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;
                option.textContent = displayDate;
                if (date === '{{ selected_date }}') {
                    option.selected = true;
                }
                dateSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading historical dates:', error));

    // Handle date selection change
    document.getElementById('dateSelect').addEventListener('change', function() {
        const selectedDate = this.value;
        window.location.href = `/tier?date=${selectedDate}`;
    });

    function countSetPieces(row) {
        const pieces = [
            row.querySelector('td:nth-child(3)'), // Head
            row.querySelector('td:nth-child(4)'), // Shoulders
            row.querySelector('td:nth-child(5)'), // Chest
            row.querySelector('td:nth-child(6)'), // Hands
            row.querySelector('td:nth-child(7)')  // Legs
        ];
        const count = pieces.filter(piece => piece && piece.querySelector('.has-piece')).length;
        return count;
    }

    function updateCounters() {
        const rows = document.querySelectorAll('.table-modern tbody tr:not([style*="display: none"])');
        const totalRaiders = rows.length;

        // Update total raiders display
        const totalRaidersDisplay = document.getElementById('total-raiders-display');
        if (totalRaidersDisplay) {
            totalRaidersDisplay.textContent = totalRaiders;
        }

        const tierCounters = {
            'Mystic': 0,
            'Dreadful': 0,
            'Venerated': 0,
            'Zenith': 0
        };

        let noSetCount = 0;
        let oneSetCount = 0;
        let twoSetCount = 0;
        let threeSetCount = 0;
        let fourSetCount = 0;

        rows.forEach(row => {
            // Count tier tokens
            const tierCell = row.querySelector('.tier-token');
            if (tierCell) {
                const tierText = tierCell.textContent.trim();
                if (tierCounters.hasOwnProperty(tierText)) {
                    tierCounters[tierText]++;
                }
            }

            // Count set pieces
            const setPieces = countSetPieces(row);
            if (setPieces === 0) noSetCount++;
            if (setPieces === 1) oneSetCount++;
            if (setPieces === 2) twoSetCount++;
            if (setPieces === 3) threeSetCount++;
            if (setPieces === 4 || setPieces === 5) fourSetCount++;
        });

        // Update tier counters
        Object.entries(tierCounters).forEach(([tier, count]) => {
            const counter = document.querySelector(`.tier-${tier.toLowerCase()} .counter-value`);
            if (counter) {
                counter.textContent = count;
            }
        });

        // Update set counters with ratios
        document.getElementById('no-set-count').textContent = noSetCount;
        document.getElementById('total-raiders').textContent = totalRaiders;
        document.getElementById('one-set-count').textContent = oneSetCount;
        document.getElementById('total-raiders-1').textContent = totalRaiders;
        document.getElementById('two-set-count').textContent = twoSetCount;
        document.getElementById('total-raiders-2').textContent = totalRaiders;
        document.getElementById('three-set-count').textContent = threeSetCount;
        document.getElementById('total-raiders-3').textContent = totalRaiders;
        document.getElementById('four-set-count').textContent = fourSetCount;
        document.getElementById('total-raiders-4').textContent = totalRaiders;
    }

    // Update counters initially
    updateCounters();

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update counters after filtering
            updateCounters();
        });
    }
});
</script>

<!-- Additional CSS for tier-specific styling -->
<style>
/* Compact tier summary */
.tier-summary-compact {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

/* Single row of all counters */
.tier-counters-row {
    flex-wrap: nowrap;
    overflow-x: auto;
}

/* Tier token counters */
.tier-counter-compact {
    flex: 1;
    min-width: 0;
    text-align: center;
    padding: 0.75rem 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.tier-counter-compact .counter-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.tier-counter-compact .counter-label {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Set bonus counters */
.set-counter-compact {
    flex: 1;
    min-width: 0;
    text-align: center;
    padding: 0.75rem 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.set-counter-compact .counter-value {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.set-counter-compact .counter-label {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Set counter colors */
.set-counter-compact.no-set .counter-value {
    color: var(--danger-color);
}
.set-counter-compact.one-set .counter-value {
    color: var(--danger-color);
}
.set-counter-compact.two-set .counter-value {
    color: var(--warning-color);
}
.set-counter-compact.three-set .counter-value {
    color: var(--warning-color);
}
.set-counter-compact.four-set .counter-value {
    color: var(--success-color);
}

/* Visual separator between token and set counters */
.counter-separator {
    width: 2px;
    background: var(--border-color);
    margin: 0 0.5rem;
    border-radius: 1px;
}

/* Compact table with less padding */
.table-modern th {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    font-weight: 600;
}

.table-modern td {
    padding: 0.4rem 0.75rem;
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Even width for tier piece columns */
.table-modern th:nth-child(3),  /* Head */
.table-modern th:nth-child(4),  /* Shoulders */
.table-modern th:nth-child(5),  /* Chest */
.table-modern th:nth-child(6),  /* Hands */
.table-modern th:nth-child(7) { /* Legs */
    width: 12%;
    text-align: center;
}

.table-modern td:nth-child(3),  /* Head */
.table-modern td:nth-child(4),  /* Shoulders */
.table-modern td:nth-child(5),  /* Chest */
.table-modern td:nth-child(6),  /* Hands */
.table-modern td:nth-child(7) { /* Legs */
    width: 12%;
    text-align: center;
}

.tier-piece {
    display: flex;
    align-items: center;
    justify-content: center;
}

.tier-piece i {
    font-size: 1rem;
}
</style>
{% endblock %}
