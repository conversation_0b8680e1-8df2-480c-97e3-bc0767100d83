{% extends "base.html" %}

{% block title %}Raid Logs - Uproar{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-scroll text-accent me-2"></i>
                    Raid Logs
                </h1>
            </div>
            <div class="fetch-logs-container">
                <button id="fetch-logs-btn" class="btn btn-success btn-sm" onclick="fetchLogs()"
                        title="Fetch latest logs from Warcraft Logs (2 minute cooldown)">
                    <i class="fas fa-download me-1"></i>Fetch Logs
                </button>
                <div id="fetch-status" class="mt-1 text-center" style="display: none;">
                    <small class="text-warning">Fetching logs...</small>
                </div>
                <div id="cooldown-timer" class="mt-1 text-center" style="display: none;">
                    <small class="text-warning">Cooldown: <span id="timer-seconds">120</span>s</small>
                </div>
                <div id="error-message" class="mt-1 text-center" style="display: none;">
                    <small class="text-danger"></small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compact Filters -->
<div class="row mb-3">
    <div class="col-12">
        <div class="filters-compact p-3">
            <form method="get" action="/logs" class="d-flex align-items-center gap-3 flex-wrap">
                <div class="d-flex align-items-center gap-2">
                    <label for="zone-filter" class="form-label mb-0 text-secondary">
                        <i class="fas fa-map me-1"></i>Zone:
                    </label>
                    <select id="zone-filter" name="zone" class="form-select form-select-sm" style="min-width: 150px;">
                        <option value="">All Zones</option>
                        {% for zone in zones %}
                        <option value="{{ zone }}" {% if selected_zone == zone %}selected{% endif %}>{{ zone }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <label for="kill-filter" class="form-label mb-0 text-secondary">
                        <i class="fas fa-trophy me-1"></i>Result:
                    </label>
                    <select id="kill-filter" name="kill" class="form-select form-select-sm">
                        <option value="all" {% if selected_kill == 'all' %}selected{% endif %}>All Results</option>
                        <option value="true" {% if selected_kill == 'true' %}selected{% endif %}>Kills Only</option>
                        <option value="false" {% if selected_kill == 'false' %}selected{% endif %}>Wipes Only</option>
                    </select>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <label for="duplicate-filter" class="form-label mb-0 text-secondary">
                        <i class="fas fa-copy me-1"></i>Duplicates:
                    </label>
                    <select id="duplicate-filter" name="show_duplicates" class="form-select form-select-sm">
                        <option value="false" {% if not show_duplicates %}selected{% endif %}>Hide</option>
                        <option value="true" {% if show_duplicates %}selected{% endif %}>Show All</option>
                    </select>
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                    {% if selected_zone or selected_kill != 'true' or show_duplicates %}
                    <a href="/logs?kill=true&show_duplicates=false" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Reset
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Logs Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Encounter Logs
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search logs..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="date" class="text-start">
                                    <i class="fas fa-calendar me-1"></i>Date
                                </th>
                                <th data-sort="encounter" class="text-start">
                                    <i class="fas fa-dragon me-1"></i>Encounter
                                </th>
                                <th data-sort="duration" class="text-start">
                                    <i class="fas fa-clock me-1"></i>Duration
                                </th>
                                <th data-sort="kill" class="text-start">
                                    <i class="fas fa-trophy me-1"></i>Result
                                </th>
                                <th data-sort="owner" class="text-start">
                                    <i class="fas fa-user me-1"></i>Owner
                                </th>
                                <th data-sort="zone" class="text-start">
                                    <i class="fas fa-map me-1"></i>Zone
                                </th>
                                <th class="text-start">
                                    <i class="fas fa-external-link-alt me-1"></i>Log Link
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td class="text-start">
                                    <span class="text-secondary">{{ log.date }}</span>
                                </td>
                                <td class="text-start">
                                    <span class="fw-bold">{{ log.encounter }}</span>
                                </td>
                                <td class="text-start">
                                    <span class="badge bg-tertiary border-custom">{{ log.duration }}</span>
                                </td>
                                <td class="text-start">
                                    {% if log.kill %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Kill
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Wipe
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="text-start">
                                    <span class="text-primary">{{ log.owner }}</span>
                                </td>
                                <td class="text-start">
                                    <span class="badge bg-secondary">{{ log.zone }}</span>
                                </td>
                                <td class="text-start">
                                    <a href="{{ log.link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-1"></i>View Log
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table-modern');
    const headers = table.querySelectorAll('th[data-sort]');
    let currentSort = { column: null, asc: true };

    // Auto-select zone filter on change
    document.getElementById('zone-filter').addEventListener('change', function() {
        this.form.submit();
    });

    // Auto-select kill filter on change
    document.getElementById('kill-filter').addEventListener('change', function() {
        this.form.submit();
    });

    // Auto-select duplicate filter on change
    document.getElementById('duplicate-filter').addEventListener('change', function() {
        this.form.submit();
    });

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Check for existing cooldown on page load
    checkExistingCooldown();
});

// Fetch logs functionality with cooldown
let cooldownActive = false;
let cooldownTimer = null;

// Check for existing cooldown on page load
function checkExistingCooldown() {
    const cooldownEnd = localStorage.getItem('fetchLogsCooldownEnd');
    if (cooldownEnd) {
        const now = Date.now();
        const endTime = parseInt(cooldownEnd);

        if (now < endTime) {
            // Cooldown is still active
            const remainingSeconds = Math.ceil((endTime - now) / 1000);
            startCooldownWithTime(remainingSeconds);
        } else {
            // Cooldown has expired, clean up
            localStorage.removeItem('fetchLogsCooldownEnd');
        }
    }
}

function fetchLogs() {
    if (cooldownActive) {
        return;
    }

    const fetchBtn = document.getElementById('fetch-logs-btn');
    const fetchStatus = document.getElementById('fetch-status');
    const cooldownDiv = document.getElementById('cooldown-timer');
    const timerSpan = document.getElementById('timer-seconds');
    const errorDiv = document.getElementById('error-message');

    // Hide any previous error messages
    errorDiv.style.display = 'none';

    // Disable button and show status
    fetchBtn.disabled = true;
    fetchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Fetching...';
    fetchStatus.style.display = 'block';

    // Make the API call
    fetch('/fetch-logs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        fetchStatus.style.display = 'none';

        if (data.message && !data.error) {
            // Show success message briefly
            fetchBtn.innerHTML = '<i class="fas fa-check me-2"></i>Success! Refresh the page!';
            fetchBtn.classList.remove('btn-success');
            fetchBtn.classList.add('btn-success');

            // Start cooldown and store end time
            const cooldownEndTime = Date.now() + (120 * 1000); // 2 minutes from now
            localStorage.setItem('fetchLogsCooldownEnd', cooldownEndTime.toString());
            startCooldown();
        } else {
            // Show error
            fetchBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error';
            fetchBtn.classList.remove('btn-success');
            fetchBtn.classList.add('btn-danger');

            // Show error message
            errorDiv.querySelector('small').textContent = data.error || 'Unknown error occurred';
            errorDiv.style.display = 'block';

            // Reset button after 5 seconds
            setTimeout(() => {
                resetFetchButton();
                errorDiv.style.display = 'none';
            }, 5000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        fetchStatus.style.display = 'none';
        fetchBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error';
        fetchBtn.classList.remove('btn-success');
        fetchBtn.classList.add('btn-danger');

        // Show error message
        errorDiv.querySelector('small').textContent = 'Network error occurred';
        errorDiv.style.display = 'block';

        // Reset button after 5 seconds
        setTimeout(() => {
            resetFetchButton();
            errorDiv.style.display = 'none';
        }, 5000);
    });
}

function startCooldown() {
    startCooldownWithTime(120); // 2 minutes
}

function startCooldownWithTime(seconds) {
    cooldownActive = true;
    const cooldownDiv = document.getElementById('cooldown-timer');
    const timerSpan = document.getElementById('timer-seconds');

    cooldownDiv.style.display = 'block';

    cooldownTimer = setInterval(() => {
        seconds--;
        timerSpan.textContent = seconds;

        if (seconds <= 0) {
            clearInterval(cooldownTimer);
            cooldownActive = false;
            cooldownDiv.style.display = 'none';
            localStorage.removeItem('fetchLogsCooldownEnd');
            resetFetchButton();
        }
    }, 1000);

    // Set initial display
    timerSpan.textContent = seconds;
}

function resetFetchButton() {
    const fetchBtn = document.getElementById('fetch-logs-btn');
    fetchBtn.disabled = false;
    fetchBtn.innerHTML = '<i class="fas fa-download me-2"></i>Fetch Logs';
    fetchBtn.classList.remove('btn-danger');
    fetchBtn.classList.add('btn-success');
}
</script>

<!-- Additional CSS for logs-specific styling -->
<style>
/* Compact fetch logs container */
.fetch-logs-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
}

#fetch-logs-btn {
    min-width: 100px;
    transition: all 0.3s ease;
}

#fetch-logs-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

#cooldown-timer {
    font-size: 0.75rem;
}

#fetch-status {
    font-size: 0.75rem;
}

/* Compact filters */
.filters-compact {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.filters-compact .form-label {
    font-size: 0.85rem;
    font-weight: 500;
    white-space: nowrap;
}

.filters-compact .form-select {
    min-width: 100px;
}

/* Compact table with less padding */
.table-modern th {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    font-weight: 600;
}

.table-modern td {
    padding: 0.4rem 0.75rem;
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Smaller badges and buttons */
.table-modern .badge {
    font-size: 0.75rem;
}

.table-modern .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}






