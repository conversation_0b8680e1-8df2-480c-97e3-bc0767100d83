#!/usr/bin/env python3
"""
Test script to verify the fetch logs API endpoint
"""

import requests
import json
import os

def test_connection():
    """Test basic connectivity to the Flask app"""
    base_url = "http://*************:5555"

    print("Testing connection to Flask app...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✓ Flask app is accessible (Status: {response.status_code})")
        return True
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to Flask app: {e}")
        return False

def test_fetch_logs_api():
    """Test the /fetch-logs endpoint"""

    # First test basic connectivity
    if not test_connection():
        print("\nSkipping /fetch-logs test due to connectivity issues")
        return

    url = "http://*************:5555/fetch-logs"

    print("\nTesting /fetch-logs API endpoint...")

    # Check if SUDO_PASSWORD is set (this would be needed on the server side)
    sudo_password = os.getenv('SUDO_PASSWORD')
    if not sudo_password:
        print("⚠ SUDO_PASSWORD environment variable not set locally")
        print("  (This may be set on the server side)")

    print("\n1. Testing /fetch-logs endpoint:")
    try:
        response = requests.post(url, timeout=30)  # Increased timeout for potentially long operation
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            try:
                json_response = response.json()
                print(f"✓ Success: {json_response.get('message', 'No message')}")
                if 'timestamp' in json_response:
                    print(f"  Timestamp: {json_response['timestamp']}")
                if 'output' in json_response:
                    output = json_response['output']
                    if len(output) > 200:
                        print(f"  Output (first 200 chars): {output[:200]}...")
                    else:
                        print(f"  Output: {output}")
            except json.JSONDecodeError:
                print(f"✓ Response received but not JSON: {response.text[:200]}...")
        else:
            try:
                error_response = response.json()
                print(f"✗ Error: {error_response.get('error', 'Unknown error')}")
            except json.JSONDecodeError:
                print(f"✗ Error response: {response.text}")

    except requests.exceptions.Timeout:
        print("✗ Request timed out (operation may take longer than 30 seconds)")
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")

def main():
    print("=== Flask App /fetch-logs Endpoint Test ===")
    print("This test checks:")
    print("1. Basic connectivity to the Flask app")
    print("2. The /fetch-logs endpoint functionality")
    print("3. Proper error handling")
    print()

    test_fetch_logs_api()

if __name__ == "__main__":
    main()
