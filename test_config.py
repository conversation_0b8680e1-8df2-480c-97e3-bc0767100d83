#!/usr/bin/env python3
"""Test script to verify configuration loading"""

import json

def load_config():
    """Load configuration from config.json file"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Config file not found")
        return None
    except Exception as e:
        print(f"Error loading config: {e}")
        return None

def main():
    config = load_config()
    
    if config:
        print("✅ Configuration loaded successfully!")
        print(f"Discord Raider Role ID: {config['discord']['raider_role_id']}")
        print(f"Discord Guild ID: {config['discord']['guild_id']}")
        print(f"Warcraft Logs Guild ID: {config['warcraft_logs']['guild_id']}")
        print(f"Tier Item Level: {config['game_settings']['tier_ilvl']}")
        print(f"App Host: {config['app_settings']['host']}")
        print(f"App Port: {config['app_settings']['port']}")
        print(f"App Debug: {config['app_settings']['debug']}")
    else:
        print("❌ Failed to load configuration!")

if __name__ == "__main__":
    main()
