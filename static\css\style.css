body {
    background-color: #1a1a1a;
    color: #ffffff;
    font-family: Arial, sans-serif;
}

.table-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.rank-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #111111;
    border-radius: 8px;
    overflow: hidden;
}

.rank-table th {
    background-color: #7c03037e;
    color: #ffffff;
    padding: 0.7rem;
    text-align: left;
    border-bottom: 2px solid #4a4a4a;
}

.rank-table td {
    padding: 0.1rem;
    padding-left: 0.5rem;
    border-bottom: 1px solid #4a4a4a;
}

.rank-table tr:hover {
    background-color: #3a3a3a;
}

.boss-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
    vertical-align: middle;
}

.dungeon-name {
    display: flex;
    align-items: center;
}

.epic {
    color: #a335ee;
}

.legendary {
    color: #ff8000;
}

.rare {
    color: #0070dd;
}

.primary {
    color: #ffffff;
}

.character-name {
    display: flex;
    align-items: center;
    padding: 5px;
}

.faction {
    color: #c41f3b;  /* Red for Horde */
}

.faction-alliance {
    color: #0070dd;  /* Blue for Alliance */
}

.armor-type {
    color: #ffffff;  /* White for armor types */
}

.search-container {
    margin-bottom: 1rem;
}

.search-bar {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #4a4a4a;
    border-radius: 4px;
    background-color: #2a2a2a;
    color: #ffffff;
    font-size: 1rem;
}

.search-bar:focus {
    outline: none;
    border-color: #666666;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
}

.class-deathknight {
    color: #C41E3A;
}

.class-demonhunter {
    color: #A330C9;
}

.class-druid {
    color: #FF7C0A;
}

.class-evoker {
    color: #33937F;
}

.class-hunter {
    color: #AAD372;
}

.class-mage {
    color: #3FC7EB;
}

.class-monk {
    color: #00FF98;
}

.class-paladin {
    color: #F48CBA;
}

.class-priest {
    color: #FFFFFF;
}

.class-rogue {
    color: #FFF468;
}

.class-shaman {
    color: #0070DD;
}

.class-warlock {
    color: #8788EE;
}

.class-warrior {
    color: #C69B6D;
}

.role-icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin-right: 5px;
}

/* Navigation Styles */
.nav-container {
    background-color: #0000006b;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nav-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.nav-link:hover {
    background-color: #c41f3b;  /* Red for Horde */
    ;
}

.nav-link.active {
    background-color: #c41f3b;
}

/* Make the table container work with the new nav */
.table-container {
    margin-top: 1rem;
}
.role-counters {
    color: white;
    font-size: 1.1em;
}

.role-counter {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.3);
}

.role-counter .role-icon {
    width: 24px;
    height: 24px;
}

.counter-value {
    font-weight: bold;
    margin-right: 0.2em;
}

/* Role counter specific colors */
.role-counter.role-tank { color: #C79C6E; }
.role-counter.role-healer { color: #00ff96; }
.role-counter.role-melee { color: #C41F3B; }
.role-counter.role-ranged { color: #69CCF0; }

/* Reset table role colors back to white */
.rank-table .role {
    color: #ffffff;
}

/* Tier token counters */
.tier-counters {
    color: white;
    font-size: 1.1em;
}

.tier-counter {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.3);
}

.tier-counter .counter-value {
    font-weight: bold;
    margin-right: 0.2em;
}

/* Tier counter specific colors */
.tier-counter.tier-mystic { color: #c41f3b; }
.tier-counter.tier-dreadful { color: #00ff96; }
.tier-counter.tier-venerated { color: #ffd700; }
.tier-counter.tier-zenith { color: #69CCF0; }

/* Armor type counters */
.armor-counter {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.3);
}

.armor-counter .counter-value {
    font-weight: bold;
    margin-right: 0.2em;
}

/* Armor counter specific colors */
.armor-counter.armor-plate { color: #c41f3b; }
.armor-counter.armor-mail { color: #00ff96; }
.armor-counter.armor-leather { color: #ffd700; }
.armor-counter.armor-cloth { color: #69CCF0; }

/* Buff/Debuff counters */
.buff-counters {
    color: white;
    font-size: 1.1em;
}

.buff-counter,
.debuff-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 48px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    padding: 0;  /* Remove any padding */
    overflow: hidden;  /* Ensure content doesn't overflow */
}

.buff-counter.available,
.debuff-counter.available {
    border-color: #00ff96;
    box-shadow: 0 0 10px rgba(0, 255, 150, 0.3);
    background: rgba(0, 255, 150, 0.1);
}

.buff-counter.unavailable,
.debuff-counter.unavailable {
    border-color: #444 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    opacity: 0.3 !important;
    box-shadow: none !important;
}

.buff-icon,
.debuff-icon {
    width: 100%;  /* Fill the entire container width */
    height: 100%;  /* Fill the entire container height */
    border-radius: 6px;  /* Match container border radius minus border width */
    transition: filter 0.3s ease;
    display: block;
    object-fit: fill;  /* Show full icon without cropping */
}

.buff-counter.unavailable .buff-icon,
.debuff-counter.unavailable .debuff-icon {
    filter: grayscale(100%) brightness(0.3) !important;
}

/* Separator line */
.separator {
    width: 2px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0;
    flex: 0 0 2px;  /* Don't allow separator to grow or shrink */
}

/* Make the container wider to accommodate all counters */
.tier-counters .d-flex {
    flex-wrap: wrap;
    justify-content: center;
}

/* Counter containers shared styles */
.role-counters,
.tier-counters {
    max-width: 1200px;  /* Match table width */
    margin: 0 auto;
    padding: 0 1rem;
}

.buff-counters {
    max-width: 1200px;  /* Match table width */
    margin: 0 auto;
    padding: 0 1rem;
}

/* Counter rows layout */
.role-counters .d-flex,
.tier-counters .d-flex {
    display: flex;
    justify-content: space-between;  /* Spread items evenly */
    gap: 1rem;
    width: 100%;
}

/* Single counter group for buff counters */
.buff-counters .single-counter-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;  /* Match table width */
    margin: 0 auto;
    gap: 1rem;
}

/* Counter items shared styles */
.role-counter,
.tier-counter,
.armor-counter {
    flex: 1;  /* Make all counters take equal space */
    text-align: center;
    padding: 0.5em 1em;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.3);
    min-width: 120px;  /* Ensure minimum width for content */
}

/* Individual buff/debuff counter styling */
.buff-counter,
.debuff-counter {
    flex: 1;  /* Each counter takes equal space */
    max-width: 60px;  /* Limit maximum width */
    margin: 0;  /* Remove margins since we use gap */
}

/* Counter groups (left and right side of separator) */
.counter-group {
    display: flex;
    gap: 1rem;
    flex: 1;  /* Each group takes equal space */
    justify-content: space-between;
}

/* Status cell styles */
.status-cell {
    text-transform: capitalize;
    font-weight: bold;
}

.status-cell.approved {
    color: #28a745;  /* Green for approved */
}

.status-cell.pending {
    color: #ffc107;  /* Yellow for pending */
}

/* Button styles */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

/* iLvl styling */
.ilvl {
    font-weight: bold;
    color: #ffd700;  /* Gold color for item level */
}

/* Custom form control sizes */
.form-control-sm {
    height: 28px !important;
    padding: 0.2rem 0.5rem !important;
}

.btn-sm {
    height: 28px !important;
    padding: 0.2rem 1.5rem !important;
    white-space: nowrap !important;
}

.nav-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    white-space: nowrap;
}

.ilvl-counter {
    background-color: #c41f3b;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    margin-right: 0; /* Remove right margin */
}

.ilvl-counter .counter-value {
    font-weight: bold;
    color: #fff;
}

#lastUpdated {
    white-space: nowrap;
}

/* Add these styles if they don't already exist */

/* Kill column icons */
.fa-check {
    color: #28a745;
}

.fa-times {
    color: #dc3545;
}

/* Sortable table headers */
th.sortable {
    cursor: pointer;
    position: relative;
}

th.sortable:after {
    content: '⇕';
    position: absolute;
    right: 8px;
    color: #999;
}

th.sort-asc:after {
    content: '↑';
    color: #333;
}

th.sort-desc:after {
    content: '↓';
    color: #333;
}

/* Filter container */
.filter-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}