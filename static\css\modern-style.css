/* Modern Dark Theme for Uproar Guild Management */

:root {
    /* Color Palette */
    --primary-bg: #0d1117;
    --secondary-bg: #161b22;
    --tertiary-bg: #21262d;
    --accent-red: #c41f3b;
    --accent-red-hover: #a01729;
    --accent-gold: #ffd700;
    --text-primary: #f0f6fc;
    --text-secondary: #f0f6fc;
    --text-muted: #e4e9f0;
    --border-color: #30363d;
    --success-color: #238636;
    --warning-color: #d29922;
    --danger-color: #da3633;
    --info-color: #1f6feb;
    --bs-body-color: #e9ecef;
    --bs-secondary-color: #e9ecef;
    
    /* WoW Class Colors */
    --class-death-knight: #C41E3A;
    --class-demon-hunter: #A330C9;
    --class-druid: #FF7C0A;
    --class-evoker: #33937F;
    --class-hunter: #AAD372;
    --class-mage: #3FC7EB;
    --class-monk: #00FF98;
    --class-paladin: #F48CBA;
    --class-priest: #FFFFFF;
    --class-rogue: #FFF468;
    --class-shaman: #0070DD;
    --class-warlock: #8788EE;
    --class-warrior: #C69B6D;
    
    /* Role Colors */
    --role-tank: #C79C6E;
    --role-healer: #00ff96;
    --role-melee: #C41F3B;
    --role-ranged: #69CCF0;
    
    /* Spacing */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Custom Bootstrap Overrides */
.bg-dark-custom {
    background: var(--secondary-bg) !important;
    border-bottom: 1px solid var(--border-color);
}

.navbar-dark .navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--text-primary) !important;
    background-color: var(--tertiary-bg);
    transform: translateY(-1px);
}

.navbar-dark .navbar-nav .nav-link.active {
    color: var(--text-primary) !important;
    background-color: var(--accent-red);
    box-shadow: var(--shadow);
}

.navbar-text {
    color: var(--text-muted) !important;
}

/* Main Content */
.main-content {
    flex: 1;
    padding-top: 100px; /* Account for fixed navbar */
    padding-bottom: 2rem;
}

/* Cards */
.card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--tertiary-bg);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    padding: 0.5rem 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--tertiary-bg) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-gold));
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* Role-specific colors for stats cards */
.stats-card.tank { color: var(--role-tank); }
.stats-card.healer { color: var(--role-healer); }
.stats-card.melee { color: var(--role-melee); }
.stats-card.ranged { color: var(--role-ranged); }

/* Tables */
.table-modern {
    background: var(--secondary-bg);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.table-modern thead th {
    background: var(--tertiary-bg);
    color: var(--text-primary);
    font-weight: 600;
    border: none;
    padding: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table-modern tbody td {
    background: var(--secondary-bg);
    color: var(--text-primary);
    border: none;
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.table-modern tbody tr:hover {
    background: var(--tertiary-bg);
}

.table-modern tbody tr:last-child td {
    border-bottom: none;
}

/* Class Colors */
.class-death-knight { color: var(--class-death-knight) !important; }
.class-demon-hunter { color: var(--class-demon-hunter) !important; }
.class-druid { color: var(--class-druid) !important; }
.class-evoker { color: var(--class-evoker) !important; }
.class-hunter { color: var(--class-hunter) !important; }
.class-mage { color: var(--class-mage) !important; }
.class-monk { color: var(--class-monk) !important; }
.class-paladin { color: var(--class-paladin) !important; }
.class-priest { color: var(--class-priest) !important; }
.class-rogue { color: var(--class-rogue) !important; }
.class-shaman { color: var(--class-shaman) !important; }
.class-warlock { color: var(--class-warlock) !important; }
.class-warrior { color: var(--class-warrior) !important; }

/* Role Icons */
.role-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    vertical-align: middle;
    border-radius: 4px;
}

/* Item Level Styling */
.ilvl {
    font-weight: 700;
    color: var(--accent-gold);
    font-size: 1.1rem;
}

/* Buttons */
.btn-primary {
    background: var(--accent-red);
    border-color: var(--accent-red);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--accent-red-hover);
    border-color: var(--accent-red-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline-primary {
    color: var(--accent-red);
    border-color: var(--accent-red);
}

.btn-outline-primary:hover {
    background: var(--accent-red);
    border-color: var(--accent-red);
}

/* Form Controls */
.form-control {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius);
}

.form-control:focus {
    background: var(--tertiary-bg);
    border-color: var(--accent-red);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(196, 31, 59, 0.25);
}

.form-control::placeholder {
    color: var(--text-muted);
}
.form-select
{
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius);
}

/* Footer */
.footer {
    background: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding-top: 80px;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-modern {
        font-size: 0.875rem;
    }
    
    .table-modern thead th,
    .table-modern tbody td {
        padding: 0.75rem 0.5rem;
    }
}

/* Utility Classes */
.text-accent { color: var(--accent-red) !important; }
.text-gold { color: var(--accent-gold) !important; }
.bg-tertiary { background-color: var(--tertiary-bg) !important; }
.border-custom { border-color: var(--border-color) !important; }

/* Buff/Debuff Counters */
.buff-counter, .debuff-counter {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    background: var(--tertiary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.buff-counter.available, .debuff-counter.available {
    border-color: var(--success-color);
    box-shadow: 0 0 10px rgba(35, 134, 54, 0.3);
    background: rgba(35, 134, 54, 0.1);
}

.buff-counter.unavailable, .debuff-counter.unavailable {
    border-color: #444 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    opacity: 0.3 !important;
    box-shadow: none !important;
}

.buff-icon, .debuff-icon {
    width: 100%;
    height: 100%;
    border-radius: calc(var(--border-radius) - 2px);
    object-fit: cover;
    transition: filter 0.3s ease;
}

.buff-counter.unavailable .buff-icon,
.debuff-counter.unavailable .debuff-icon {
    filter: grayscale(100%) brightness(0.3) !important;
}

/* Tier and Armor Counters */
.tier-counter, .armor-counter {
    padding: 1rem;
    text-align: center;
    background: var(--tertiary-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.tier-counter:hover, .armor-counter:hover {
    background: var(--secondary-bg);
    transform: translateY(-1px);
}

.tier-counter .counter-value,
.armor-counter .counter-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.tier-counter small,
.armor-counter small {
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Enhanced Table Sorting */
.table-modern th[data-sort] {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all 0.3s ease;
}

.table-modern th[data-sort]:hover {
    background: var(--primary-bg);
}

.table-modern th[data-sort]::after {
    content: '⇅';
    position: absolute;
    right: 8px;
    opacity: 0.5;
    font-size: 0.8rem;
}

.table-modern th[data-sort].sort-asc::after {
    content: '↑';
    opacity: 1;
    color: var(--accent-red);
}

.table-modern th[data-sort].sort-desc::after {
    content: '↓';
    opacity: 1;
    color: var(--accent-red);
}
