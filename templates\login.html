{% extends "base.html" %}

{% block title %}Login - Uproar{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center" style="min-height: 60vh;">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-shield-alt text-accent" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="h4 mb-2">Raiders Access</h3>
                    <p class="text-secondary">Enter password to access raider management</p>
                </div>

                <form method="POST">
                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        <input type="password"
                               class="form-control form-control-lg"
                               id="password"
                               name="password"
                               placeholder="Enter your password"
                               required
                               autocomplete="current-password">
                    </div>

                    {% if error %}
                    <div class="alert alert-danger d-flex align-items-center mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ error }}
                    </div>
                    {% endif %}

                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login
                    </button>
                </form>

                <div class="text-center mt-4">
                    <small class="text-secondary">
                        <i class="fas fa-info-circle me-1"></i>
                        Contact guild leadership for access
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on password field when page loads
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.focus();
    }

    // Add some visual feedback on form submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
            }
        });
    }
});
</script>

<!-- Additional CSS for login-specific styling -->
<style>
.card {
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.form-control:focus {
    border-color: var(--accent-red);
    box-shadow: 0 0 0 0.2rem rgba(196, 31, 59, 0.25);
}

.btn-primary {
    background-color: var(--accent-red);
    border-color: var(--accent-red);
}

.btn-primary:hover {
    background-color: var(--accent-red-hover);
    border-color: var(--accent-red-hover);
}

.text-accent {
    color: var(--accent-red) !important;
}
</style>
{% endblock %}
