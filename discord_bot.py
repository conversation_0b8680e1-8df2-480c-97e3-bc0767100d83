import os
import discord
from discord.ext import commands, tasks
from discord.ui import <PERSON><PERSON>, View
from dotenv import load_dotenv
import csv
from datetime import datetime
import io
import asyncio
import time
from collections import defaultdict
import json

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')

# Function to load configuration from JSON file
def load_config():
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Config file not found, using default values")
        return {
            "discord": {
                "raider_role_id": "1173303263832064050",
                "guild_id": "451810734129676298",
                "support_role_id": "1060628081359982714",
                "ticket_category_id": "451810734129676300",
                "target_server_id": "451810734129676298"
            },
            "game_settings": {
                "ticket_cooldown": 300,
                "max_tickets_per_user": 1
            },
            "file_paths": {
                "exports_dir": "discord_exports",
                "export_filename": "csv_user_export.csv"
            }
        }

# Load configuration
config = load_config()

# Set up bot with intents
intents = discord.Intents.default()
intents.members = True  # Enable member intents
intents.message_content = True  # Enable message content intent
intents.guilds = True
intents.messages = True
bot = commands.Bot(command_prefix='!!', intents=intents)

# Create exports directory if it doesn't exist
EXPORTS_DIR = config.get('file_paths', {}).get('exports_dir', 'discord_exports')
if not os.path.exists(EXPORTS_DIR):
    os.makedirs(EXPORTS_DIR)

# Configuration values
TARGET_SERVER_ID = int(config.get('discord', {}).get('target_server_id', '451810734129676298'))
EXPORT_FILENAME = config.get('file_paths', {}).get('export_filename', 'csv_user_export.csv')

# 🔧 TICKET SYSTEM CONFIGURATION
SUPPORT_ROLE_ID = int(config.get('discord', {}).get('support_role_id', '1060628081359982714'))
TICKET_CATEGORY_ID = int(config.get('discord', {}).get('ticket_category_id', '451810734129676300'))

# 🛡️ SPAM PROTECTION SETTINGS
TICKET_COOLDOWN = config.get('game_settings', {}).get('ticket_cooldown', 300)
MAX_TICKETS_PER_USER = config.get('game_settings', {}).get('max_tickets_per_user', 1)

# 📊 TRACKING DICTIONARIES
user_cooldowns = {}  # {user_id: last_ticket_time}
active_tickets = defaultdict(list)  # {user_id: [thread_ids]}
creating_tickets = set()  # {user_id} - users currently creating tickets

@bot.event
async def on_ready():
    print(f'{bot.user} has connected to Discord!')
    # Start the hourly export task
    hourly_export.start()
    # Clean up any stale ticket tracking on startup
    await cleanup_stale_tickets()

@tasks.loop(hours=1)
async def hourly_export():
    """Automatically export users every hour"""
    guild = bot.get_guild(TARGET_SERVER_ID)
    if guild is None:
        print(f"Could not find server with ID {TARGET_SERVER_ID}")
        return

    filename = os.path.join(EXPORTS_DIR, EXPORT_FILENAME)
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Username', 'Display Name', 'ID', 'Roles', 'Raider']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for member in guild.members:
                # Get all roles for the member
                roles = [role.name for role in member.roles if role.name != '@everyone']
                is_raider = 'Uproar Raider' in roles
                
                # Write all users to CSV
                writer.writerow({
                    'Username': str(member),  # Full username
                    'Display Name': member.display_name,
                    'ID': str(member.id),  # Ensure ID is string
                    'Roles': ', '.join(roles) if roles else '',
                    'Raider': str(is_raider)  # 'True' or 'False'
                })
                print(f"Exported user: {member.display_name}")
                        
        print(f"Hourly export completed: {filename}")
        
    except Exception as e:
        print(f"Error during automated export: {str(e)}")

# 🛡️ SPAM PROTECTION & TICKET TRACKING FUNCTIONS
async def cleanup_stale_tickets():
    """Clean up tracking for deleted threads on bot startup"""
    category = bot.get_channel(TICKET_CATEGORY_ID)
    if not category:
        return

    # Get all current thread IDs in the category
    current_threads = set()
    for channel in category.channels:
        if isinstance(channel, discord.TextChannel):
            for thread in channel.threads:
                if thread.name.startswith("ticket-"):
                    current_threads.add(thread.id)

    # Clean up tracking for non-existent threads
    for user_id in list(active_tickets.keys()):
        active_tickets[user_id] = [tid for tid in active_tickets[user_id] if tid in current_threads]
        if not active_tickets[user_id]:
            del active_tickets[user_id]

def is_user_on_cooldown(user_id):
    """Check if user is on cooldown"""
    if user_id not in user_cooldowns:
        return False

    time_since_last = time.time() - user_cooldowns[user_id]
    return time_since_last < TICKET_COOLDOWN

def get_cooldown_remaining(user_id):
    """Get remaining cooldown time in seconds"""
    if user_id not in user_cooldowns:
        return 0

    time_since_last = time.time() - user_cooldowns[user_id]
    remaining = TICKET_COOLDOWN - time_since_last
    return max(0, remaining)

def has_active_tickets(user_id):
    """Check if user has active tickets or is currently creating one"""
    active_count = len(active_tickets.get(user_id, []))
    is_creating = user_id in creating_tickets
    return active_count >= MAX_TICKETS_PER_USER or is_creating

def add_user_ticket(user_id, thread_id):
    """Add a ticket to user's active tickets"""
    active_tickets[user_id].append(thread_id)
    user_cooldowns[user_id] = time.time()

def remove_user_ticket(user_id, thread_id):
    """Remove a ticket from user's active tickets"""
    if user_id in active_tickets:
        if thread_id in active_tickets[user_id]:
            active_tickets[user_id].remove(thread_id)
        if not active_tickets[user_id]:
            del active_tickets[user_id]

def start_ticket_creation(user_id):
    """Mark user as currently creating a ticket"""
    creating_tickets.add(user_id)

def finish_ticket_creation(user_id):
    """Remove user from ticket creation tracking"""
    creating_tickets.discard(user_id)

async def generate_transcript(thread):
    """Generate a transcript of the thread"""
    messages = [msg async for msg in thread.history(limit=None, oldest_first=True)]
    transcript_lines = []

    for msg in messages:
        timestamp = msg.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        author = f"{msg.author.display_name} ({msg.author})"
        content = msg.content if msg.content else "[No text content]"

        # Handle attachments
        if msg.attachments:
            attachments = ", ".join([att.filename for att in msg.attachments])
            content += f" [Attachments: {attachments}]"

        transcript_lines.append(f"[{timestamp}] {author}: {content}")

    return "\n".join(transcript_lines)

# 🎫 TICKET MANAGEMENT UI CLASSES
class TicketManagementView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(SaveTranscriptButton())
        self.add_item(CloseTicketButton())

class SaveTranscriptButton(Button):
    def __init__(self):
        super().__init__(label="💾 Save Transcript", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to save transcripts.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        # Send transcript to the person requesting it via DM
        try:
            await interaction.user.send(
                f"📄 **Ticket Transcript**: {thread.name}\n"
                f"Requested by: {interaction.user.mention}\n"
                f"Generated at: {discord.utils.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}",
                file=file
            )
            await interaction.response.send_message("📄 Transcript sent to your DMs!", ephemeral=True)
        except discord.Forbidden:
            # Fallback: send in thread if DM fails
            file_fallback = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")
            await interaction.response.send_message(
                "⚠️ Couldn't send DM. Here's the transcript (please enable DMs for future use):",
                file=file_fallback,
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(f"❌ Error generating transcript: {str(e)}", ephemeral=True)

class CloseTicketButton(Button):
    def __init__(self):
        super().__init__(label="🔒 Close Ticket", style=discord.ButtonStyle.danger)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to close tickets.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        # Send transcript to the person closing the ticket via DM
        try:
            await interaction.user.send(
                f"📄 **Ticket Transcript**: {thread.name}\n"
                f"Closed by: {interaction.user.mention}\n"
                f"Closed at: {discord.utils.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}",
                file=file
            )
            dm_status = "✅ Transcript sent to your DMs."
        except discord.Forbidden:
            dm_status = "⚠️ Couldn't send DM. Please enable DMs from server members."
        except Exception as e:
            dm_status = f"⚠️ Error sending DM: {str(e)}"

        await interaction.response.send_message(
            f"📄 Ticket will be closed in 5 seconds.\n{dm_status}"
        )

        # Find the ticket creator from the thread name and remove from tracking
        thread_name = thread.name
        if thread_name.startswith("ticket-"):
            # Extract user info from thread name to find the creator
            for user_id, ticket_list in active_tickets.items():
                if thread.id in ticket_list:
                    remove_user_ticket(user_id, thread.id)
                    break

        await asyncio.sleep(5)
        await thread.delete()

# 🎫 TICKET SYSTEM MESSAGE HANDLER
@bot.event
async def on_message(message):
    # Ignore bot messages
    if message.author.bot:
        return

    # Check if message is exactly !!ticket (not part of another command)
    if message.content.strip() == "!!ticket":
        await message.delete()

        # Check spam protection
        user_id = message.author.id

        # Check if user is on cooldown
        if is_user_on_cooldown(user_id):
            remaining = get_cooldown_remaining(user_id)
            minutes = int(remaining // 60)
            seconds = int(remaining % 60)
            await message.channel.send(
                f"⏰ {message.author.mention} You're on cooldown! Please wait {minutes}m {seconds}s before creating another ticket.",
                delete_after=10
            )
            return  # Don't process as command

        # Check if user has active tickets or is already creating one
        if has_active_tickets(user_id):
            active_count = len(active_tickets.get(user_id, []))
            is_creating = user_id in creating_tickets

            if is_creating:
                await message.channel.send(
                    f"⚠️ {message.author.mention} You're already creating a ticket! Please wait for it to complete.",
                    delete_after=10
                )
            else:
                await message.channel.send(
                    f"🎫 {message.author.mention} You already have {active_count} active ticket(s). Please close your existing ticket(s) before creating a new one.",
                    delete_after=10
                )
            return  # Don't process as command

        # Mark user as creating a ticket IMMEDIATELY to prevent spam
        start_ticket_creation(user_id)

        try:
            await create_ticket(message.author, message.guild, message.channel)
        except Exception as e:
            # If ticket creation fails, remove the creation lock
            finish_ticket_creation(user_id)
            await message.channel.send(
                f"❌ {message.author.mention} Failed to create ticket: {str(e)}",
                delete_after=15
            )
        return  # Don't process as command since we handled the ticket creation

    # Process other commands (only if it wasn't a ticket request)
    await bot.process_commands(message)

# 🎫 TICKET CREATION FUNCTION
async def create_ticket(user, guild, original_channel):
    """Create a ticket thread for the user"""
    category = bot.get_channel(TICKET_CATEGORY_ID)

    if not category or not isinstance(category, discord.CategoryChannel):
        await original_channel.send("⚠️ Ticket category not found or misconfigured.", delete_after=10)
        return

    # Find a suitable channel in the category to create the thread
    # We'll use the first text channel in the category
    parent_channel = None
    for channel in category.channels:
        if isinstance(channel, discord.TextChannel):
            parent_channel = channel
            break

    if not parent_channel:
        await original_channel.send("⚠️ No suitable channel found in the ticket category.", delete_after=10)
        return

    try:
        # Create a thread from the parent channel
        thread = await parent_channel.create_thread(
            name=f"ticket-{user.name}-{user.discriminator}",
            type=discord.ChannelType.private_thread,
            invitable=False
        )

        # Add the ticket creator
        await thread.add_user(user)

        # Add all support role members
        support_role = guild.get_role(SUPPORT_ROLE_ID)
        support_members = []
        if support_role:
            for member in support_role.members:
                try:
                    await thread.add_user(member)
                    support_members.append(member)
                except discord.HTTPException:
                    print(f"Failed to add {member} to thread")

        # Create mentions for all thread participants
        mentions = [user.mention]
        if support_role:
            mentions.append(support_role.mention)

        # Send initial message with all mentions to ensure visibility
        initial_message = (
            f"🎫 **New Ticket Created**\n"
            f"**Created by:** {user.mention}\n"
            f"**Support Team:** {support_role.mention if support_role else 'No support role configured'}\n"
            f"**Thread Participants:** {', '.join([user.mention] + [m.mention for m in support_members])}\n\n"
            f"📋 This is a private support ticket. Use the buttons below to manage this ticket."
        )

        await thread.send(initial_message, view=TicketManagementView())

        # Add ticket to tracking and remove creation lock
        add_user_ticket(user.id, thread.id)
        finish_ticket_creation(user.id)

        # Send confirmation in original channel
        await original_channel.send(
            f"✅ {user.mention} Your ticket has been created: {thread.mention}",
            delete_after=10
        )

    except discord.Forbidden:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} I don't have permission to create threads in that category. "
            f"Please contact an administrator to check bot permissions.",
            delete_after=15
        )
    except discord.HTTPException as e:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} Failed to create ticket: {str(e)}",
            delete_after=15
        )
    except Exception as e:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} An unexpected error occurred: {str(e)}",
            delete_after=15
        )
        print(f"Ticket creation error: {e}")

# 🎫 TICKET MANAGEMENT COMMANDS
@bot.command()
async def check_perms(ctx):
    """Check bot permissions in the ticket category"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    category = bot.get_channel(TICKET_CATEGORY_ID)
    if not category:
        await ctx.send("❌ Ticket category not found!")
        return

    # Check bot permissions in category
    bot_perms = category.permissions_for(ctx.guild.me)

    required_perms = [
        ('read_messages', bot_perms.read_messages),
        ('send_messages', bot_perms.send_messages),
        ('create_private_threads', bot_perms.create_private_threads),
        ('manage_threads', bot_perms.manage_threads),
        ('send_messages_in_threads', bot_perms.send_messages_in_threads),
        ('manage_messages', bot_perms.manage_messages)
    ]

    perm_status = []
    for perm_name, has_perm in required_perms:
        status = "✅" if has_perm else "❌"
        perm_status.append(f"{status} {perm_name}")

    embed = discord.Embed(
        title="🔧 Bot Permissions Check",
        description=f"**Category:** {category.name}\n\n" + "\n".join(perm_status),
        color=0x00ff00 if all(p[1] for p in required_perms) else 0xff0000
    )

    await ctx.send(embed=embed)

@bot.command()
async def ticket_stats(ctx):
    """Show ticket system statistics (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    # Count active tickets
    total_active = sum(len(tickets) for tickets in active_tickets.values())
    users_with_tickets = len(active_tickets)

    # Count users on cooldown
    current_time = time.time()
    users_on_cooldown = sum(1 for last_time in user_cooldowns.values()
                           if current_time - last_time < TICKET_COOLDOWN)

    # Count users currently creating tickets
    users_creating = len(creating_tickets)

    embed = discord.Embed(
        title="🎫 Ticket System Statistics",
        color=0x00ff00
    )
    embed.add_field(name="Active Tickets", value=f"{total_active}", inline=False)
    embed.add_field(name="Users on Cooldown", value=f"{users_on_cooldown}", inline=False)
    embed.add_field(name="Currently Creating", value=f"{users_creating}", inline=False)
    embed.add_field(name="Cooldown Duration", value=f"{TICKET_COOLDOWN//60} minutes", inline=False)
    embed.add_field(name="Max Tickets per User", value=f"{MAX_TICKETS_PER_USER}", inline=False)

    await ctx.send(embed=embed)

@bot.command()
async def reset_cooldown(ctx, user: discord.Member):
    """Reset a user's ticket cooldown (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    if user.id in user_cooldowns:
        del user_cooldowns[user.id]
        await ctx.send(f"✅ Cooldown reset for {user.mention}")
    else:
        await ctx.send(f"ℹ️ {user.mention} is not on cooldown.")

@bot.command()
async def force_close_tickets(ctx, user: discord.Member):
    """Force close all tickets for a user (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    if user.id not in active_tickets:
        await ctx.send(f"ℹ️ {user.mention} has no active tickets.")
        return

    ticket_ids = active_tickets[user.id].copy()
    closed_count = 0

    for thread_id in ticket_ids:
        thread = bot.get_channel(thread_id)
        if thread:
            try:
                await thread.delete()
                closed_count += 1
            except:
                pass
        remove_user_ticket(user.id, thread_id)

    await ctx.send(f"✅ Force closed {closed_count} ticket(s) for {user.mention}")

@bot.command(name='export_users')
async def export_users(ctx):
    """Export all users from the server to a CSV file"""
    # Check if the command is used in the target server
    if ctx.guild.id != TARGET_SERVER_ID:
        await ctx.send(f"This command can only be used in the target server (ID: {TARGET_SERVER_ID})!")
        return

    # Use fixed filename
    filename = os.path.join(EXPORTS_DIR, EXPORT_FILENAME)
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Username', 'Display Name', 'ID', 'Roles']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            # Write each member's information
            for member in ctx.guild.members:
                roles = [role.name for role in member.roles if role.name != '@everyone']
                writer.writerow({
                    'Username': str(member),
                    'Display Name': member.display_name,
                    'ID': member.id,
                    'Roles': ', '.join(roles)
                })
        await ctx.send(f"User export complete! File saved as: {filename}")
        
    except Exception as e:
        await ctx.send(f"Error during export: {str(e)}")

@bot.command(name='raiders')
async def raiders(ctx):
    """Post the raiders screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_raiders.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending raiders screenshot: {str(e)}")

@bot.command(name='tier')
async def tier(ctx):
    """Post the tier screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_tier.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending tier screenshot: {str(e)}")

@bot.command(name='enchants')
async def enchants(ctx):
    """Post the enchants screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_enchants.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending enchants screenshot: {str(e)}")

@bot.command(name='rules')
async def rules(ctx):
    """Post the rules screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_rules.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending raiders screenshot: {str(e)}")

# 🎫 SETUP PERSISTENT VIEWS
@bot.event
async def setup_hook():
    bot.add_view(TicketManagementView())  # Register persistent button view on startup

# Run the bot
if __name__ == "__main__":
    bot.run(TOKEN)
